<table><tr><td>专利类型</td><td>发明</td><td>联系人</td><td>任鲁西</td></tr><tr><td>联系电话</td><td>158 3979 4854</td><td>E-mail</td><td><EMAIL></td></tr></table>

# 一种基于Transformer的可调控阈值的霉变烟检测方法

# 发明名称

一种基于Transformer的可调控阈值的霉变烟检测方法

# 技术领域

[0001]本发明属于机器视觉领域，深度学习方向，一种基于Transformer的可调控阈值的霉变烟检测方法，应用于成品烟箱开箱表面霉变检测及切片后包芯霉变烟检测。

# 背景技术

[0002]复烤厂打后片烟需要经过一段时间的仓储醇化才会到卷烟厂进行制丝工

序的生产加工。片烟醇化过程中，放置时间较长且无法实现每一箱的精准管理，因此会有部分烟叶产生霉变现象，这会给企业带来较大的损失。尤其当霉变烟叶在生产过程中没有及时发现被切丝后，更是难以追溯，其霉菌病原生物都附着在烟丝上，最终成品烟支流入市场，将会造成灾难级的影响。[0003]本发明基于Transformer的可调控阈值的霉变烟检测方法并结合工业视觉检测技术搭建的视觉采集装置，形成了一套集软、硬件于一体的霉变烟检测系统，其实现了成品烟箱开箱表面霉变检测的功能，同时具备在切片后检测包芯霉变烟的功能。

# 发明内容

[0004]本发明提出一种基于Transformer的可调控阈值的霉变烟检测方法包含如下步骤：

1）霉变数据采集；2）数据预处理；3）建立基于Transformer的霉变烟检测模型主干网络；4）分类结构嵌入非线性激活函数作为可调控阈值；

5）可调控阈值结果有效性验证；

[0005]本发明具体流程图如下图1：

![](images/d59d8d371fc0e6388ac826ea6c7052f85a207028df04da3a40b96f014310cf10.jpg)  
图1流程图

[0006]所述步骤1）本发明通过真彩相机进行基础霉变图像采集，FLIR面阵相机作为系统采集端的核心，配合漫反射长条光对检测环境进行无反光模式的补光方法，形成高色彩还原、高清晰保真的烟叶图像。如下图2是霉变烟检测

线。

![](images/856335fb5e1f5eca64c5e5a471949b82b836af9e95292b15550b470447fb59ca.jpg)  
图2 霉变烟检测线

[0007]所述步骤2）数据预处理包括数据标准化和数据增强。

[0008]数据标准化是比较常用的预处理方法，其将图像中的像素值调整成均值

$\mathrm{x}' = \frac{\mathrm{x} - \mu}{\sigma}$  为0，方差为1的分布，其中标准化公式为 ，其中  $\mu$  和  $\sigma$  分别是样本数据的均值（mean）和标准差（std）。

[0009]由于霉变样本在实际生产中极难获得，因此霉变样本与正常图像样本极不

平衡，本发明使用的数据霉变样本：正常样本比值为1:9。此情况严重影响模型

的效果，因此考虑对霉变样本进行大量的数据增强。本发明采用的数据增强除了基础的仿射变换，还有亮度增强、人为添加噪声等。

[0010]所述步骤3）建立基于Transformer的霉变烟检测模型主干网络，本发明采用的Transformer是由自注意力模块和前向传播模块组成的编码器构成的。自注意力模块和前向传播模块都使用了残差结构。

[0011]自注意力模块的计算公式为  $Attention(Q,K,V) = Softmax\left(\frac{QK^T}{\sqrt{d_k}}\right)V$ 。其中

Q、K、V为输入特征经过线性变换后分成三份；  $d_k$  是隐藏维度，为了防止输入Softmax的数值过大，导致偏导数趋于0。

[0012]前向传播模块本质上是MLP（多层感知机）。前向传播模块由于残差结构的存在，使用的MLP保持了输出与输入维度一致；并使用GELU激活函数进行有效的非线性化。

[0013]通过对主干网络特征层提取，可以看出模型能够有效定位霉变区域，如下

图3为真彩相机采集的烟叶原图，图4为Transformer主干网络的热力图。

![](images/7839f7a97c7ea888a52131ae46829e2f06b2571108581960ed972724470415c0.jpg)  
图3 原图

![](images/a65430445080ae2c8b33d97d95350e4c967ad5b5ac33375a3de8c570bc263ca2.jpg)  
图4 热力图

[0014]所述步骤4）分类结构嵌入非线性激活函数作为可调控阈值。基础分类结构是由一个正则化层和一个线性层组成，通常在计算损失时会根据情况加入激活函数只作为损失的计算，不参与模型的推理。而本发明将激活函数作为分类

结构的一部分参与模型推理，并将其作为可调控阈值根据现场情况进行人为控制。

[0015]本发明为2分类问题优先选择Sigmoid激活函数，Sigmoid激活函数公式为  $S(x) = \frac{1}{1 + e^{- x}}$ ，其图形如图5。Sigmoid函数的优点在于它可导，并且值域在0到1之间，使得神经元的输出标准化。可调控阈值就是在值域0到1之间选择一个数值r，（0，r）区间定义为霉变烟，[r，1）区间定义为正常烟。在模型部署之前会在测试集上挑选一个r值作为推荐阈值，当模型部署到不同生产线时可根据实际情况进行阈值的调整。

![](images/f6096e8a79999d5e8b756c09e8d2dd477f8ee5cfde87c0a8dad0fe26e1990e91.jpg)  
图5函数图像

[0016]所述步骤5）将模型部署到生产线上进行有效性验证，如表1，为测试的

其中一批次验证结果。

<table><tr><td>霉变阈值r</td><td>正常烟误检率(%)</td><td>霉变烟检出率(%)</td></tr><tr><td>0.65</td><td>0</td><td>86.44%</td></tr><tr><td>0.5</td><td>0</td><td>86.44%</td></tr><tr><td>0.3</td><td>0</td><td>86.44%</td></tr><tr><td>0.2</td><td>0</td><td>86.44%</td></tr><tr><td>0.1</td><td>0.87%</td><td>96.83%</td></tr><tr><td>0.01</td><td>1.5%</td><td>96.83%</td></tr></table>

[0017]从表1可以看出随着阈值r减小，正常烟的误检测在上升，而霉变烟的检出率在下降。模型部署时根据测试集测试结果会给出建议阈值  $\mathrm{r} = 0.1$  ，当模型Sigmoid输出的值小于0.1时被判定为霉变，此时保证了低误检率的同时有较高的霉变检出率。

[0018]本发明可调控阈值r更适用现场的原因在于：当实际生产对误检率有更高的要求时（如误检会影响正常生产），可增大r值；当实际生产对霉变检出率要求更严格时，可适当减小r值。

9/9