# 研发项目立项决议书

<table><tr><td>项目名称</td><td colspan="3">烟叶中主梗检测分割智能识别系统研发</td></tr><tr><td>项目所属单位</td><td colspan="3">上海创和亿电子科技发展有限公司</td></tr><tr><td>项目承担部门</td><td colspan="3">研发部</td></tr><tr><td>项目负责人姓名</td><td colspan="3">彭云发</td></tr><tr><td>项目经费预算(万元)</td><td>156.80</td><td>项目起止时间</td><td>2023年1月至2023年12月</td></tr><tr><td colspan="4">项目研发内容及相关技术指标见附件：《烟叶中主梗检测分割智能识别系统研发》</td></tr><tr><td>公司研发立项小组意见：</td><td></td><td></td><td></td></tr></table>

同意立项。

                  研发立项小组负责人：

<table><tr><td></td><td></td><td></td></tr><tr><td>&lt;ecel&gt;</td><td></td><td></td></tr></table>

同意立项。

                  上海创和亿电子科技发展有限公司（盖章）

                  年 月 日<lcel><lcel><lcel><nl>

地址：上海市杨浦区昆明路508号北美广场B座11楼Tel.:86-21-55967195Fax:86-21-65137825Web：www.shmicrovision.com

# 项目开发计划任务书

项目名称：烟叶中主梗检测分割智能识别系统研发  起止年月：自2023年1月至2023年12月止  承担部门：研发部  课题负责人：彭云发

# 上海创和亿电子科技发展有限公司

二0二三年一月

# 项目开发计划任务书

# 一、立项依据及开发目的

在烟草工业生产中，烟叶出片率的准确计算对于生产成本控制、质量管理和收益评估具有重要意义。传统的烟叶主梗检测方法主要依赖人工手工剥离并称重统计，这种方式存在以下关键问题：

效率低下：人工剥离烟叶主梗耗时较长，无法满足现代化生产线高速、连续检测的需求，严重制约了生产效率的提升。

破坏性检测：手工剔除会破坏烟叶的整体性，影响后续加工工艺，同时无法实现无损检测的要求。

主观误差大：人工检测存在个体差异和主观判断误差，检测结果的一致性和可靠性难以保证。

人力成本高：需要大量熟练工人进行检测作业，人力成本不断上升，且存在人员流动性大的问题。

数据追溯困难：传统方法难以建立完整的数据记录和追溯体系，不利于质量管控和工艺优化。

随着智能制造和工业4.0的发展，烟草行业迫切需要一种基于计算机视觉的自动化检测方法，能够实现对烟叶主梗的精准识别与分割，快速计算烟梗面积占比，为烟叶出片率计算提供科学依据。

针对上述问题，本项目提出"一种烟叶中主梗检测分割方法"，通过深度学习技术与GPU加速优化，实现烟叶主梗的智能化检测。具体创新点包括：

深度学习模型创新：采用Swin Transformer + UperNet的先进架构，实现对烟叶主梗的精准语义分割。

GPU加速优化：通过CUDA编程和TensorRT推理框架，实现毫秒级的实时检测速度。

智能化计算方法：基于像素统计的面积比值计算，为烟叶出片率提供精确数据支撑。

# 开发目的：

提升检测精度：通过深度学习方法实现对烟叶主梗的精准识别，确保检测结果的准确性和一致性。

实现无损检测：替代传统破坏性人工检测，保持烟叶完整性，满足工业现场实时、连续检测需求。

降低生产成本：减少人工依赖，降低人力成本，提高生产效率和经济效益。

推动行业智能化：为烟草工业智能制造提供关键技术支撑，助力行业转型升级。

# 二、本项目组织实施方式

本项目由公司自主开发完成，公司研发部负责项目的技术开发与算法优化工作，软件部主要负责系统软件开发和界面设计等工作，设备部负责硬件集成和系统部署等工作，分工明确，内部协调。公司财务部负责研发经费的筹措和管理，管理层提供人力、设备方面的保障。

# 三、本项目技术关键及创新点

# （1）深度学习模型架构创新技术

创新性地采用Swin Transformer + UperNet的先进图像分割架构，结合层次化设计理念，实现对烟叶主梗的精准识别与分割。通过4个stage的层次化结构，逐层扩大感受野，有效处理不同尺度的图像特征。

采用预训练模型加速训练过程，利用在烟叶分级任务上训练的模型作为预训练权重，提高模型收敛速度和检测精度，在验证集上准确率达到95%，生产环境下准确率达到87%。

# (2）GPU加速优化与工程化部署技术

针对工业生产实时性要求，采用GPU加速优化方案，通过CUDA编程和TensorRT推理框架，将Python训练模型转换为C++可读取的二进制文件。

实现数据量化优化，将推理时数据格式从FP32量化到FP16，并进行神经网络算子融合处理，检测分割速度达到毫秒级别，满足工业生产实时性要求。

# (3）智能化面积比值计算与数据分析技术

开发基于像素统计的主梗面积占比计算方法，通过公式vein_prop = Area_vein / Area_tobacco精确计算烟梗在整个烟叶中的面积比例。

结合传统图像处理方法，采用分水岭算法进行烟叶主体分割，确保面积计算的准确性和可靠性。

# （4）专业数据集构建与标准化技术

建立包含130张背光烟叶图片的专业数据集，采用labelme软件进行精确标注，形成标准化的VOC格式数据集。

按照6:2:2比例划分训练集、测试集和验证集，确保模型训练的科学性和泛化能力，为后续技术迭代奠定坚实基础。

本项目的技术创新点在于将深度学习、GPU加速优化与智能化数据分析相结合，形成一套完整的烟叶主梗检测分割解决方案，为烟草工业智能制造提供关键技术支撑。

# 四、本项目预计投入研发人员及计划进度表

本项目预计安排8名研发人员参与研发，项目预计研发期限自2023年1月至2023年12月止，具体如下：

1、2023年1月，项目正式启动。

2、2023年2-3月，梳理项目需求痛点并形成需求清单，遴选典型应用烟厂进行需求调研，完成数据集构建和标注工作。

3、2023年4-5月，根据调研结果进行需求分析和算法设计，完成Swin Transformer + UperNet模型搭建和训练。

4、2023年6-7月，完成模型优化和GPU加速方案开发，进行算法验证与性能测试。

5、2023年8-9月，完成软件系统设计开发和用户界面设计，完成硬件设备集成调试。

6、2023年10-11月，完成软硬件系统联调测试并进行现场验证，完成专利申请工作。

7、2023年12月，项目研究数据分析汇总，准备结题材料，完成项目研究技术报告。

# 五、预期成果归属

本项目预期获得知识产权成果及相关经济收益均归上海创和亿电子科技发展有限公司所有。

# 六、经费预算：

<table><tr><td>序号</td><td>费用项目</td><td>发生额</td></tr><tr><td>1</td><td>研发活动直接消耗的材料、燃料和动力费用</td><td>180,000</td></tr><tr><td>2</td><td>直接从事研发活动的本企业在职人员费用</td><td>1,200,000</td></tr><tr><td>3</td><td>专门用于研发活动的有关折旧费</td><td>15,000</td></tr><tr><td>4</td><td>专门用于研发活动的有关租赁费</td><td>20,000</td></tr><tr><td>5</td><td>专门用于研发活动的有关无形资产摊销费</td><td>10,000</td></tr><tr><td>6</td><td>专门用于中间试验和产品试制的模具、工艺装备开发及制造费</td><td>80,000</td></tr><tr><td>7</td><td>研发成果论证、鉴定、评审、验收费用</td><td>25,000</td></tr><tr><td>8</td><td>与研发活动直接相关的其它费用</td><td>38,000</td></tr><tr><td>9</td><td>总计</td><td>1,568,000</td></tr></table>

地址：上海市杨浦区昆明路508号北美广场B座11楼Tel.:86-21-55967195Fax:86-21-65137825Web：www.shmicrovision.com
