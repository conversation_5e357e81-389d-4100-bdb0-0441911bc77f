# 说明书

# 一种烟叶中主梗检测分割方法

# 技术领域

本发明方法主要是辅助在烟厂计算烟叶的出片率，属于计算机视觉技术，可以快速精确的地检测分割出烟叶上的主梗，是计算烟叶出片率中十分重要的一个环节，对生产的意义重大。

# 背景技术

目前，大部分烟厂对烟叶的出片率十分看重，合理的计算监控烟叶的出片率可以很好地指导烟厂收烟烤烟。传统方法是人工手工剥离烟叶的主梗并称重然后统计记录，这种方式比较依赖人力，而且手工剔除会破坏烟叶的整体性，也比较耗费时间和人力。因此，应用计算机视觉方法来解放人力意义重大。

本发明方法通过计算机视觉对烟叶进行图像分割，然后将提取出来的烟梗用于计算整片烟叶中的占比，通过重量传感器对整片烟叶的称重来修正拟合烟梗的重量。目前该方法在应用过程中精度很高且具有实时性，工程化之后的检测分割速度可以达到毫秒级别的处理，可大规模应用在烟叶生产的多个任务中，具有很高的适用性。

# 发明内容

本发明提供的是一种烟叶主梗检测分割的方法，利用深度学习图像分割技术，识别并精细分割出烟叶中主梗的图像，通过计算烟叶主梗的

# 说明书

占比来进一步计算整批烟叶的出片率。该方法可以不依赖于人工剔除，加快工业流程速度，提高生产力，进一步实现工业自动化。

本发明提供一种烟叶主梗检测分割方法，包含以下步骤：

步骤A、根据烟厂采集到的烟叶图像，设计实验并制作数据集。

步骤B、根据划分的训练和测试数据集进行建模训练和算法优化。

步骤C、验证训练完后算法模型的泛化能力以及其准确性。

步骤D、根据获取的当前现场的图像，采用预先训练好的主梗分割模型分割出烟叶图像的主梗。

步骤E、根据获取的当前传送带上的图像，采用传统图像的方法去除烟叶的背景，分割出只包含单片烟叶的图像。

步骤F、根据主梗的分割图像计算主梗与烟叶面积的比值。

步骤G、烟叶主梗检测分割算法部署优化加速。

步骤H、烟叶主梗检测分割最终结果的存储与显示。

进一步，本发明提供的一种烟叶上主梗检测分割的方法，还可以具有这样的特征：

步骤A从烟厂获取到烟叶之后，设计背光实验，调整背光光源的

亮度透过烟叶，采集烟梗数据集。标注图片中的烟梗，形成烟梗分割数据集。

进一步，本发明提供的一种烟叶上主梗检测分割的方法，还可以具有这样的特征：其中，步骤B根据划分的训练和测试数据集进行建模训练和算法优化。包含以下步骤：

步骤B- 1将训练的数据集进行预处理操作，然后建立模型，本发明方法的基础模型为swin transformer + upernet模型，设定基础的参数，包括学习率、训练的最大次数、优化器和loss函数。

步骤B- 2加载烟叶分级分类预训练模型，该模型为烟叶分级任务上训练得到的模型，去掉分类头之后主干网络和本文方法中的swin transformer结构一致。预训练模型可以使得该训练快速收敛，间接程度上加速训练过程。开始训练，根据计算得到的实时评价指标来调整对应的参数，直至模型训练达到最优，人工调参可以一定程度上解决模型的欠拟合和过拟合问题。

进一步，本发明提供的一种烟叶上主梗检测分割的方法，还可以具有这样的特征：其中，步骤C中验证训练完后算法模型的泛化能力以及其准确性。包含以下步骤：

步骤C- 1将训练完成之后的指标最优的模型在验证集上进行验证，对其结果进行分析，根据效果进行分析，如果效果达不到要求，则需要

# 说明书

返回步骤B调整算法并进行优化。如果效果十分良好则需要的是对算法进一步测试。

步骤C- 2模拟真实生产环境下的烟叶，该烟叶与训练、测试验证集的主要区别在于没有背光，且有些烟叶图像含有一些背景。针对多批烟叶进行多场景测试，测试泛化能力以及针对数字化的结果形成测试报告。

进一步，本发明提供的一种烟叶上主梗检测分割的方法，还可以具有这样的特征：其中，步骤D中根据获取的当前现场上的图像，采用预先训练好的主梗分割模型分割出烟叶图像的主梗。该主梗有两个作用，首先作为后续计算烟叶主梗占比，最后作为效果展示。

进一步，本发明提供的一种烟叶上主梗检测分割的方法，还可以具有这样的特征，步骤E包括以下步骤：

步骤E- 1、对图像进行预处理，进行简单的去噪。

步骤E- 2、对去噪完的图像使用分水岭算法进行分割，提取出烟叶部分然后进行二值化获取烟叶分割图。

进一步，本发明提供的一种烟叶上主梗检测分割的方法，还可以具有这样的特征：步骤F包括以下步骤：

步骤F- 1、根据处理获取的烟叶分割图像如附图三所示，统计烟叶

# 说明书

范围内像素的个数。

步骤F- 2、根据处理获取的烟叶主梗分割图像如附图四所示，统计主梗的像素个数。

步骤F- 3、计算主梗所占烟叶的比例vein_prop:

vein_prop = Area_{vein} / Area_{tobacco}

其中：Area_{vein} 为主梗的像素点个数，Area_{tobacco} 为烟叶的总像素点个数。

进一步，本发明提供的一种烟叶上主梗检测分割的方法，还可以具有这样的特征，步骤G包括以下步骤：

步骤G- 1、检测分割采用的是CPU上opencv读取图像，GPU上进行算法检测分割，使得本发明中的检测分割方法可以通过加速达到毫秒级别的运行速度，满足烟厂生产过程中的实时性的要求。

步骤G- 2、本实验例中，耗时最久的是实例分割算法（swin transformer+upernet），考虑到实时性要求，采取了GPU加速优化方案。通过将python框架下训练完成的pytorch框架下的.pth文件解

析成打包成.wts二进制文件，即将网络模型结构与对应的参数转化成 $\mathtt{c + + }$  可读取的二进制文件。

步骤G- 3、通过cuda编程搭建基于tensorrt推理框架下的深度学习网络推理框架，加载.wts二进制文件， $\mathtt{c + + }$  语言重构swintransformer+upernet分割模型，再进行数据量化设置（推理时数据格式从fp32到fp16），GPU推理加速无误之后，生成DLL以供软件界面调用。

进一步，本发明提供的一种烟叶上主梗检测分割的方法，还可以具有这样的特征：其中，步骤H中相机控制模块调用检测分割的DLL，输入当前相机拍摄的烟叶图像，运行得到对应烟叶的主梗图像，生成带最终检测分割结果的烟叶含梗的图像，显示到界面上。

本发明提供一种烟叶中主梗检测分割方法，利用深度学习与GPU编程技术，识别并分割出烟叶中主梗的位置的同时计算烟叶主梗的占整个烟叶的比例，以及通过重量传感器来计算烟梗所占的重量，可以进一步辅助烟厂计算烟叶出片率，加快工业流程速度，进一步实现工业自动化。

# 附图说明

# 说明书

图一是实施例中的烟叶中主梗检测分割方法的流程图。

图二是实施例中的swintransformer的整体架构。

图三是实施例中的该图像分割模型整体架构。

图四是实施例中的烟叶中主梗检测分割方法的烟梗显示图。

图五是实施例中的烟叶中主梗检测分割方法的烟叶显示图。

图六是实施例中的烟叶中主梗检测分割方法的结果显示图。

# 具体实施方式

下面结合附图和具体实施例对本发明做进一步的描述。

实施例

一种烟叶主梗检测分割方法，包括以下检测步骤：

步骤A、根据烟厂采集到的烟叶图像，设计实验并制作数据集。

本实施例中，采用图像采集装置进行图像采集。图像采集装置包括：工业相机、相机镜头、相机防尘罩、相机遮光罩、铝型材支架、交换机、工控机和相机控制模块。

步骤A- 1设计背光实验，调整背光光源的亮度透过烟叶，直至观察到烟梗明显，然后采集足够数量的数据集。此实验的目的在于更好的突显出烟梗的轮廓，便于人眼观察并细致地标注烟梗的边缘。

步骤A- 2将实验采集到的130张背光烟叶图片，使用labelme软

件标注烟梗的边缘直至整个每片烟叶上主梗轮廓形成闭环，然后保存成json文件。

步骤A- 3将带标签文件的数据集按照6：2：2来划分成训练集、测试集以及验证集，并编写标签转化代码，将标注的图片和json文件转化为标准的VOC格式数据集，以供后续建模和训练。

步骤B、根据划分的训练和测试数据集进行建模训练和算法优化。包含以下步骤：

步骤B- 1在深度学习pytroch框架下构建swin transformer + upernet图像分割模型，设定初始参数，如学习率为0.00006，优化器为AdamW,动量为0.01，最大训练次数为16000等。

步骤B- 2、选用参数量为121M的swin- B预训练模型，该预训练模型为采用swin transformer模型在烟叶分类任务中训练了160000次拟合的模型，对烟叶特征比较敏感。在该图像分割任务中，使用该预训练模型，可以加快训练速度，当训练到8000次的时候，烟叶主梗分割模型已经趋于拟合，通过实时观察，loss值已经小于0.002，人工调参直至训练到16000次的时候，在测试集上的每个烟梗Iou（Intersection over Union）已经达到了80%，说明训练效果十分好。

步骤C验证训练完后算法模型的泛化能力以及其准确性。

步骤C- 1将训练完成之后的指标最优的模型在验证集上进行验证，

# 说明书

由于验证集是实验中拍摄的同一批背光的背面烟叶，运行完测试程序之后，效果十分显著，准确率能够达到  $95\%$  ，分割出的烟梗十分精准。

步骤C- 2将C- 1中表现优异的分割模型在真实生产环境下的烟叶数据集上进行测试，真实生产环境下的烟叶一般都是正面朝上，而且都是没有背光，烟梗不是十分的明显且烟叶处于在移动的状态，图像的质量并不是很好，但是图像分割模型仍能分割比较准确，可以达到  $87\%$  的准确率，效果图可参考附图四。分多批多场景的现场采集到的烟叶数据进行测试，表现的效果很好。

步骤D、根据获取的当前现场上的图像，采用预先训练好的主梗分割模型分割出烟叶图像的主梗。

本发明提供的一种烟叶上主梗检测分割的方法，还可以具有这样的特征：步骤D训练好的模型为swin transformer + upernet模型，该模型的训练数据为包含主梗的烟叶生产图像集。

swin transformer的整体架构如附图二所示，

整个模型结构采取层次化的设计，一共有4个stage(阶段)，每个stage都会缩小输入特征图的分辨率，感受野就会逐层扩大。整体来看，输入为batch*3*512*512，那么4个stage对应的图像大小如下：

# 说明书

stage1: batch*128*128*128stage2: batch*256*64*64,stage3: batch*512*32*32;stage4: batch*1024*16*16,。

通过附图二中(a)Architecture可以看出该整体结构主要分为3个部分：

(1) 图像处理部分，把RGB像素分辨率转化为patches分辨率图像并根据具体的模型大小来该改变输入通道数。(2) Swin Transformer stage部分，如附图二中(b)部分，实现window self-attention（W-MSA）以及shifted window self-attention(SW-MSA)，再通过Patch Merging操作来实现分层结构，降低模型的计算复杂度并能够处理不同尺度的图片。图一中*2代表的此处有两个Swin Transformer Block，模型大小不同对应的Swin Transformer Block个数不同。

(3) 不同的视觉任务输出不同，本发明方法是图像语义分割任务，所以会接一个upernet的分割头，可以实现实例分割任务。

那么该图像分割模型整体架构可以如附图三所示。

对于当前现场的图像，首先将其缩放到512*512的尺寸，进行归一化处理，然后再将处理后图像输入到已经训练好的烟叶主梗检测分割模型中，经过后处理算法计算之后可以获得主梗的轮廓。

# 说明书

步骤E、根据获取的当前传送带上的图像，采用传统图像的方法去除烟叶的背景，分割出只包含单片烟叶的图像。

步骤E- 1、根据获取的当前传送带上的图像，对图像进行预处理，进行简单的去噪平滑操作，如高斯平滑操作等。

步骤E- 2、对完成去噪的图像使用分水岭算法进行分割，其具体主要操作是，先对图像进行灰度化和二值化得到二值图像，通过膨胀得到确定的背景区域，再通过距离转换得到确定的前景区域，剩余的部分就是不确定区域；对确定的前景图像进行连接组件处理，得到标记图像；更具标记图像对原图像应用分水岭算法，更新标记的图像。提取出烟叶部分并获取烟叶分割图如附图五。

步骤F、根据主梗的分割图像计算主梗与烟叶面积的比值。

步骤F- 1、根据处理获取的烟叶分割图像，统计二值化后烟叶范围内像素的个数。

步骤F- 2、根据处理获取的烟叶主梗分割图像，统计主梗的像素个数。

步骤F- 3、计算主梗所占烟叶的比例vein_prop：

$$
\mathsf{vein\_prop} = \mathsf{Area_{vein}} / \mathsf{Area_{tobacco}}
$$

其中：Areavein为主梗的像素点个数，Areatobacco为烟叶的总像素点个数。

步骤G、烟叶主梗检测分割算法部署优化加速。

# 说明书

步骤G- 1、本实施例中，GPU加载.wts二进制文件中分割模型网络结构的参数，执行数据量化操作，将数据格式从fp32量化到fp16，并将该分割模型中的基础神经网络算子进行融合处理，经过这些优化加速方法之后，生成神经网络GPU引擎文件，该文件以供后续加载。

步骤G- 2、本实施例中，opencv读取图像队列中的图像，通过指针索引像素值传给GPU计算模块，在GPU模块中，前处理部分将HWC转化成CHW并完成归一化操作，加载GPU引擎文件，执行GPU推理计算，可在毫秒间获得模型前向计算的结果。

步骤G- 3、本实例中，在GPU模块中，获得模型前向计算的结果，通过后处理计算之后，从GPU传输到CPU上并通过指针映射到输出图像上，可以获得烟叶主梗检测分割算法的结果图。将该过程中的GPU加速计算模型封装到DLL文件中以供软件调用。

步骤H、相机控制模块调用检测分割的DLL，输入当前相机拍摄的烟叶图像，运行得到对应烟叶的主梗图像，生成带最终检测分割结果的烟叶含梗的图像，显示到界面上。

烟叶主梗检测分割的实际应用：

选取2021年某卷烟厂生产的烟叶进行测试；

首先参考图六，烟叶主梗检测分割方法最终显示结果，其中红色部分为检测分割到的主梗。图四为主梗部分二值化的图像，图五为被检测

# 说明书

的图像分割为烟叶部分的二值化图。

对于读入的图像，首先将其缩放到  $512 \times 512$  的尺寸，然后再将处理后图像输入到GPU计算模型中使用swin transformer + upernet模型进行检测分割，得到分割后的烟梗二值化图，如图四。

接下来将上烟叶主体部分分割出来，具体情况图五，通过像素统计算法分别统计烟梗和烟叶的像素个数并计算烟梗在整个烟叶上的面积占比。将结果通过  $\mathbf{c} + +$  内部引用传输给QT软件。

相机控制模块生成带框检测结果，显示到Qt软件界面上，如图六所示。通过具体结果表明该方法能较好地检测分割出烟叶中存在的烟梗并计算烟梗占比。

以上所描述的实施例仅仅是本发明一部分实施例，而非全部的实施例。基于本发明中的实施例，本领域普通技术人员在没有做出创造性劳动前提下所获得的所有其它实施例，都属于本发明保护的范围。