# 权利要求书

1、一种烟叶中主梗检测分割方法，其特征在于：包括以下检测分割步骤：

步骤A、根据烟厂采集到的烟叶图像，设计实验并制作数据集。步骤B、根据划分的训练和测试数据集进行建模训练和算法优化。

步骤C、验证训练完后算法模型的泛化能力以及其准确性。

步骤D、根据获取的当前现场的图像，采用预先训练好的主梗分割模型分割出烟叶图像的主梗。

步骤E、根据获取的当前传送带上的图像，采用传统图像的方法去除烟叶的背景，分割出只包含单片烟叶的图像。

步骤F、根据主梗的分割图像计算主梗与烟叶面积的比值。

步骤G、烟叶主梗检测分割算法部署优化加速。

步骤H、烟叶主梗检测分割最终结果的存储与显示。

2、如权利要求1所述的烟叶中主梗检测分割方法，其特征在于：

其中，步骤B根据划分的训练和测试数据集进行建模训练和算法优化。包含以下步骤：

步骤B- 1将训练的数据集进行预处理操作，然后建立模型，本发明方法的基础模型为swin transformer + upernet 模型，设定基础的参数，包括学习率、训练的最大次数、优化器和loss函数。

步骤B- 2加载烟叶分级分类预训练模型，该模型为烟叶分级任务上训练得到的模型，去掉分类头之后主干网络和本文方法中的swin transformer结构一致。预训练模型可以使得该训练快速收敛，间接程度上加速训练过程。开始训练，根据计算得到的实时评价指标来调整对应的参数，直至模型训练达到最优，人工调参可以一定程度上解决模型的欠拟合和过拟合问题。

3、如权利要求1所述的烟叶中主梗检测分割方法，其特征在于：

其中，步骤C包括以下步骤：

步骤C- 1将训练完成之后的指标最优的模型在验证集上进行验证，对其结果进行分析，根据效果进行分析，如果效果达不到要求，则需要返回步骤B调整算法并进行优化。如果效果十分良好则需要的是对算法进一步测试。

步骤C- 2模拟真实生产环境下的烟叶，该烟叶与训练、测试验证集的主要区别在于没有背光，且有些烟叶图像含有一些背景。针对多批烟叶进行多场景测试，测试泛化能力以及针对数字化的结果形成测试报告。

4、如权利要求1所述的烟叶中主梗检测分割方法，其特征在于：

# 权利要求书

其中，步骤E包括以下步骤：

步骤E- 1、对图像进行预处理，进行简单的去噪。

步骤E- 2、对去噪完的图像使用分水岭算法进行分割，提取出烟叶部分然后进行二值化获取烟叶分割图。

5、如权利要求1所述的烟叶中主梗检测分割方法，其特征在于：

其中，步骤F包括以下步骤：

步骤F- 1、根据处理获取的烟叶分割图像如附图三所示，统计烟叶范围内像素的个数。

步骤F- 2、根据处理获取的烟叶主梗分割图像如附图四所示，统计主梗的像素个数。

步骤F- 3、计算主梗所占烟叶的比例vein_prop：

vein_prop  $=$  Areavein/Area tobacco

其中：Area $_{\text{vein}}$ 为主梗的像素点个数，Area $_{\text{tobacco}}$ 为烟叶的总像素点个数。

# 权利要求书

6、如权利要求1所述的烟叶中主梗检测分割方法，其特征在于：步骤G- 1、检测分割采用的是CPU上opencv读取图像，GPU上进行算法检测分割，使得本发明中的检测分割方法可以通过加速达到毫秒级别的运行速度，满足烟厂生产过程中的实时性的要求。

步骤G- 2、本实验例中，耗时最久的是实例分割算法（swintransformer+upernet），考虑到实时性要求，采取了GPU加速优化方案。通过将python框架下训练完成的pytorch框架下的.pth文件解析成打包成.wts二进制文件，即将网络模型结构与对应的参数转化成  $\mathtt{c + + }$  可读取的二进制文件。

步骤G- 3、通过cuda编程搭建基于tensorrt推理框架下的深度学习网络推理框架，加载.wts二进制文件，  $\mathtt{c + + }$  语言重构swintransformer+upernet分割模型，再进行数据量化设置（推理时数据格式从fp32量化到fp16），GPU推理加速无误之后，生成DLL以供软件界面调用。

5、如权利要求1所述的烟叶中主梗检测分割方法，其特征在于：还包括步骤H、烟叶主梗检测分割最终结果的存储与显示。

# 权利要求书