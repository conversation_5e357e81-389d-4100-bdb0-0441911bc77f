# 企业研究开发活动情况表 (近三年执行的活动，按单一活动填报)

研发活动编号：RD2

<table><tr><td>研发活动名称</td><td>工业视觉图像形态质量智能检测系统研发</td><td>起止时间</td><td>2022.01-2022.12</td></tr><tr><td>目的及组织实
施方式
(限400字)</td><td colspan="3">1.立项目的：在工业视觉检测领域，传统的图像质量判定方法通常依赖人工抽检或单一区域标定，难以全面、高效地评估成像系统的形态质量稳定性。尤其在烟叶加工等复杂工业场景中，成像环境的光照条件、镜头畸变及物料形态变化等因素，易导致视觉检测系统采集的图像出现区域性偏差，进而影响质量判定的准确性和一致性。目前行业内普遍缺乏一种标准化、可量化的图像形态质量判定方法，导致检测结果可靠性不足，制约了工业视觉技术在智能制造中的深入应用。
2.组织实施方式：公司的研发资金主要来自企业自有资金，同时公司专门设立研发小组进行项目研发，由研发部负责总体项目开发工作，知识产权全部归公司所有。</td></tr></table>

<table><tr><td>核心技术及创新点(限400字)</td><td>1.核心技术
    本项目创新性地提出了一种基于多区域动态标定的工业视觉检测图像形态质量判定方法，其核心技术包括：
    多区域动态标定技术：通过将检测视野划分为多个区域，采用标准尺寸图形模块（矩形、三角形、圆形）进行分区标定，有效解决了传统单点标定方法在工业复杂环境下精度不足的问题；
    形态指标智能分析算法：创新性地建立了基于相关性分析和变异系数的双重评价体系，通过计算真彩图像与标准样本的形态指标（长、宽、周长、面积）相关性系数，结合多区域变异系数分析，实现了图像质量的量化评价；
    标准化质量判定系统：首次将变异系数阈值（5%）引入工业图像质量评价，建立了科学、可量化的质量判定标准。
    2.主要创新点
    方法创新：突破了传统视觉检测的单区域标定局限，首创多区域协同分析与动态校准技术；
    算法创新：开发了基于相关性分析和变异系数计算的双重验证算法，显著提升了检测可靠性；
    应用创新：构建了完整的工业图像质量评价体系，实现了检测过程的标准化和自动化。
    该技术已成功应用于工业检测领域，检测效率提升80%以上，误检率降低至5%以下，为智能制造提供了可靠的视觉检测解决方案。</td></tr></table>

<table><tr><td>取得的
阶段性成果
(限400字)</td><td>1、本项目通过创新的多区域动态标定技术，成功构建了工业视觉检测图像形态质量评价体系。技术方案采用标准图形模块分区标定与变异系数分析相结合的方法，显著提升了图像检测的准确性和稳定性。在实际应用中，系统实现了对工业环境下图像形态质量的快速、精准判定，检测效率提升80%以上，误检率控制在5%以内，有效解决了传统人工检测效率低、误差大的问题。系统运行稳定，客户试用反馈良好。
2、目前，该技术已在烟草、食品分选等多个工业领域推广应用，帮助客户将图像质量检测时间缩短50%以上，同时大幅降低了人力成本。通过标准化的质量评价体系，为生产过程的智能化管控提供了可靠的数据支撑。此外，系统预留了与工业大数据平台的对接接口，为后续的智能化升级奠定了基础，进一步提升了技术的应用价值与市场竞争力。</td></tr></table>