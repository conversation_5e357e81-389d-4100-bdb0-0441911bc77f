# 研发项目立项决议书

<table><tr><td>项目名称</td><td colspan="3">工业视觉图像形态质量智能检测系统研发</td></tr><tr><td>项目所属单位</td><td colspan="3">上海创和亿电子科技发展有限公司</td></tr><tr><td>项目承担部门</td><td colspan="3">研发部</td></tr><tr><td>项目负责人姓名</td><td colspan="3">彭云发</td></tr><tr><td>项目经费预算(万元)</td><td>129.55</td><td>项目起止时间</td><td>2022年1月至2022年12月</td></tr><tr><td colspan="4">项目研发内容及相关技术指标见附件：《工业视觉图像形态质量智能检测系统研发》</td></tr><tr><td>公司研发立项小组意见：</td><td></td><td></td><td></td></tr></table>

同意立项。

                  研发立项小组负责人：

<table><tr><td></td><td></td><td></td></tr><tr><td>&lt;ecel&gt;</td><td></td><td></td></tr></table>

同意立项。

                  上海创和亿电子科技发展有限公司（盖章）

                  年 月 日<lcel><lcel><lcel><nl>

地址：上海市杨浦区昆明路508号北美广场B座11楼Tel.:86-21-55967195Fax:86-21-65137825Web：www.shmicrovision.com

# 项目开发计划任务书

项目名称：工业视觉图像形态质量智能检测系统研发  起止年月：自2022年1月至2022年12月止  承担部门：研发部  课题负责人：彭云发

# 上海创和亿电子科技发展有限公司

二0二二年一月

# 项目开发计划任务书

# 一、立项依据及开发目的

在工业视觉检测领域，传统的图像质量判定方法通常依赖人工抽检或单一区域标定，难以全面、高效地评估成像系统的形态质量稳定性。尤其在烟叶加工等复杂工业场景中，成像环境的光照条件、镜头畸变及物料形态变化等因素，易导致视觉检测系统采集的图像出现区域性偏差，进而影响质量判定的准确性

和一致性。目前行业内普遍缺乏一种标准化、可量化的图像形态质量判定方法，导致检测结果可靠性不足，制约了工业视觉技术在智能制造中的深入应用。

以复烤行业为例，烟叶加工过程中的均质化控制高度依赖视觉检测数据，但现有技术存在以下问题：

检测精度不足：传统方法未考虑多区域成像差异，导致图像形态指标（如尺寸、颜色、纹理）的测量结果存在区域性误差，影响质量判定准确性。

效率与实时性局限：人工标定或离线分析耗时较长，无法满足生产线高速、连续检测的需求。

标准化缺失：缺乏统一的图像形态质量评价体系，不同设备或产线的检测结果难以横向对比，制约了工艺优化的数据支撑。

针对上述问题，本项目提出“一种工业视觉检测的图像形态质量判定方法”，通过多区域划分与动态标定技术，结合变异系数分析，实现成像质量的快速、客观评价。具体创新点包括：

多区域动态标定：将检测视野划分为多个区域，利用标准尺寸图形模块进行局部标定，消除镜头畸变和光照不均的影响。

形态指标量化分析：通过真彩图像与标准色卡的形态指标相关性验证，确

保采集数据的可靠性。

智能判定算法：基于变异系数动态评估成像质量的稳定性，为设备校准和工艺调整提供实时依据。

# 开发目的：

提升检测精度：通过标准化方法减少区域性误差，确保视觉检测数据真实反映物料形态质量。

实现高效无损检测：替代人工抽检，满足工业现场实时、连续检测需求，降低人力成本。

推动行业标准化：为工业视觉系统的性能评价提供可复用的技术规范，助力智能制造升级。

# 二、本项目组织实施方式

本项目由公司自主开发完成，公司研发部负责项目的技术开发与测试试验工作，软件部主要负责软件编制和测试运行等工作，设备部负责硬件结构的设计和改进等工作，分工明确，内部协调。公司财务部负责研发经费的筹措和管理，管理层提供人力、设备方面的保障。

# 三、本项目技术关键及创新点

# （1）多区域动态标定与形态质量分析技术

创新性地提出基于标准尺寸图形模块的多区域划分标定方法，通过在不同检测视野区域采集真彩图像，计算各区域的形态指标值（如尺寸、颜色、纹理等），并与标准色卡进行相关性分析，确保成像质量符合工业检测要求。

采用变异系数（CV值）动态评估图像形态质量的稳定性，实现快速、客观的成像系统性能判定，解决传统人工抽检或单一区域标定带来的误差问题。

# (2）高精度机器视觉检测算法优化

针对工业环境中的光照不均、镜头畸变等问题，优化图像预处理算法，增强特征提取的鲁棒性，确保检测数据的稳定性和可靠性。

结合计算机视觉与深度学习技术，开发高效的图像形态质量判定模型，支持实时在线检测，满足高速生产线的应用需求。

# (3）标准化质量评价体系的构建

建立基于机器视觉的工业图像形态质量判定标准，实现不同设备、产线检测结果的可比性，为工艺优化提供数据支撑。

地址：上海市杨浦区昆明路508号北美广场B座11楼  Tel.: 86- 21- 55967195  Fax: 86- 21- 65137825  Web: www.shmicrovision.com

开发配套的自动化校准工具，减少人工干预，提高检测效率，降低运维成本。

# （4）智能化数据管理与可视化分析

集成数据采集、存储与分析模块，实时监测成像质量变化趋势，并通过可视化界面（如曲线、热力图等）直观展示检测结果，便于用户快速掌握产线运行状态。

相比传统人工记录或离线分析方式，本系统具备数据持久化存储、处理速度快、稳定性高、可追溯性强等优势，显著提升工业视觉检测的智能化水平。

本项目的技术创新点在于将多区域动态标定、高精度视觉算法与智能化数据分析相结合，形成一套完整的工业图像形态质量判定解决方案，为智能制造领域的视觉检测标准化提供关键技术支撑。

# 四、本项目预计投入研发人员及计划进度表

本项目预计安排6名研发人员参与研发，项目预计研发期限自2022年1月至

2022年12月止，具体如下：

项目预计研发期限自2022年1月至2022年12月止，具体如下：

1、2022年1月，项目正式启动。

2、2022年2- 3月，梳理项目需求痛点并形成需求清单，遴选典型应用复烤厂家进行需求调研。

4、2022年4- 5月，根据调研结果进行需求分析和软件设计。对模型进行验证与优化。

5、2022年6- 7月，完成软件系统设计开发。完成硬件设备部署调试。

6、2021年8- 10月，完成软硬件系统设备联调测试并进行验证。

7、2021年11- 12月，项目研究数据分析汇总，准备结题材料，完成项目研究技术报告。

# 五、预期成果归属

本项目预期获得知识产权成果及相关经济收益均归上海创和亿电子科技发展有限公司所有。

# 六、经费预算：

<table><tr><td>序号</td><td>费用项目</td><td>发生额</td></tr><tr><td>1</td><td>研发活动直接消耗的材料、燃料和动力费用</td><td>1250,000</td></tr><tr><td>2</td><td>直接从事研发活动的本企业在职人员费用</td><td>150,000</td></tr><tr><td>3</td><td>专门用于研发活动的有关折旧费</td><td>10,000</td></tr><tr><td>4</td><td>专门用于研发活动的有关租赁费</td><td></td></tr></table>

地址：上海市杨浦区昆明路508号北美广场B座11楼Tel.:86-21-55967195Fax:86-21-65137825Web：www.shmicrovision.com

<table><tr><td>5</td><td>专门用于研发活动的有关无形资产摊销费</td><td></td></tr><tr><td>6</td><td>专门用于中间试验和产品试制的模具、工艺装备开发及制造费</td><td>30,000</td></tr><tr><td>7</td><td>研发成果论证、鉴定、评审、验收费用</td><td></td></tr><tr><td>8</td><td>与研发活动直接相关的其它费用</td><td>60,000</td></tr><tr><td>9</td><td>总计</td><td>1,295,469.74</td></tr></table>