# 企业研究开发活动情况表 (近三年执行的活动，按单一活动填报)

研发活动编号：RD4

<table><tr><td>研发活动名称</td><td>烟叶中主梗检测分割智能识别系统研发</td><td>起止时间</td><td>2023.01-2023.12</td></tr><tr><td>目的及组织实
施方式
(限400字)</td><td colspan="3">1.立项目的：在烟草工业生产中，烟叶出片率的准确计算对于生产成本控制和质量管理具有重要意义。传统的烟叶主梗检测方法主要依赖人工手工剥离并称重统计，这种方式不仅耗费大量人力物力，而且手工剔除会破坏烟叶的整体性，检测效率低下且存在主观误差。随着智能制造的发展，烟草行业迫切需要一种基于计算机视觉的自动化检测方法，能够实现对烟叶主梗的精准识别与分割，快速计算烟梗面积占比，为烟叶出片率计算提供科学依据。本项目旨在开发一种基于深度学习的烟叶主梗检测分割方法，通过Swin Transformer + UperNet模型架构，结合GPU加速优化技术，实现毫秒级的实时检测，推动烟草工业向智能化、自动化方向发展。
2.组织实施方式：公司的研发资金主要来自企业自有资金，同时公司专门设立研发小组进行项目研发，由研发部负责总体项目开发工作，知识产权全部归公司所有。</td></tr></table>

<table><tr><td>核心技术及创新点(限400字)</td><td>1.核心技术
    本项目创新性地提出了一种基于深度学习的烟叶主梗检测分割方法，其核心技术包括：
    Swin Transformer + UperNet模型架构：采用层次化设计的Swin Transformer作为主干网络，结合UperNet分割头，实现对烟叶主梗的精准语义分割，在验证集上准确率达到95%，生产环境下准确率达到87%；
    GPU加速优化技术：通过CUDA编程和TensorRT推理框架，将Python训练模型转换为C++可读取的二进制文件，实现FP32到FP16的数据量化优化，检测速度达到毫秒级别；
    智能化面积比值计算：基于像素统计的主梗面积占比计算方法，通过公式vein_prop = Area_vein / Area_tobacco精确计算烟梗在整个烟叶中的面积比例；
    多场景数据集构建：建立包含130张背光烟叶图片的专业数据集，采用labelme软件精确标注，形成标准化VOC格式数据集。
    2.主要创新点
    算法创新：首次将Swin Transformer架构应用于烟叶主梗检测，突破传统图像分割方法的局限性；
    工程创新：实现了从深度学习模型到工业化部署的完整技术链条，包括模型训练、优化加速、系统集成；
    应用创新：构建了完整的烟叶出片率智能计算解决方案，实现了检测过程的标准化和自动化。</td></tr></table>

<table><tr><td>取得的
阶段性成果
(限400字)</td><td>1、本项目通过创新的深度学习技术，成功构建了烟叶主梗检测分割智能识别系统。技术方案采用Swin Transformer + UperNet模型架构与GPU加速优化相结合的方法，显著提升了烟叶主梗检测的准确性和实时性。在实际应用中，系统实现了对烟叶主梗的精准识别与分割，验证集准确率达到95%，生产环境准确率达到87%，检测速度达到毫秒级别，有效解决了传统人工检测效率低、破坏性强、主观误差大的问题。系统运行稳定，已在合作烟厂成功部署应用，客户反馈良好。
2、目前，该技术已申请1项发明专利"一种烟叶中主梗检测分割方法"，保护了核心算法架构和关键技术创新点。通过Qt软件界面实现了检测结果的可视化展示，为烟厂提供了直观的操作界面和数据分析功能。系统替代了传统人工手工剥离烟叶主梗的检测方式，消除了人工依赖性，大幅提升了检测效率和准确性，为烟厂节约了大量人力成本。此外，该技术方案具有良好的可扩展性，可应用于其他农产品质量检测、食品加工质量控制等相关领域，具有广阔的市场应用前景和重要的产业推广价值。</td></tr></table>
